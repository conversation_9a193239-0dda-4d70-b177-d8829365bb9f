import React from "react";
import {
  Box,
  Container,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
  Alert,
  Link,
  Card,
  CardContent,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from "@mui/material";
import PrivacyTipIcon from "@mui/icons-material/PrivacyTip";
import SecurityIcon from "@mui/icons-material/Security";
import InfoIcon from "@mui/icons-material/Info";
import StorageIcon from "@mui/icons-material/Storage";
import ShareIcon from "@mui/icons-material/Share";
import ContactMailIcon from "@mui/icons-material/ContactMail";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import CheckCircleIcon from "@mui/icons-material/CheckCircle";
import WarningIcon from "@mui/icons-material/Warning";

const FacebookPrivacyPolicy: React.FC = () => {
  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Paper elevation={3} sx={{ p: 4 }}>
        {/* Header */}
        <Box sx={{ textAlign: "center", mb: 4 }}>
          <PrivacyTipIcon sx={{ fontSize: 48, color: "primary.main", mb: 2 }} />
          <Typography variant="h3" component="h1" gutterBottom>
            Facebook Privacy Policy
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            How we handle your Facebook data with transparency and care
          </Typography>
        </Box>

        {/* Important Notice */}
        <Alert severity="info" sx={{ mb: 4 }}>
          <Typography variant="body2">
            <strong>Your Privacy Matters:</strong> This policy explains how we
            collect, use, and protect your Facebook data when you connect your
            Facebook account to our application. We are committed to
            transparency and protecting your privacy.
          </Typography>
        </Alert>

        {/* Data Collection */}
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Typography
              variant="h5"
              component="h2"
              gutterBottom
              sx={{ display: "flex", alignItems: "center" }}
            >
              <StorageIcon sx={{ mr: 1 }} />
              Information We Collect
            </Typography>
            <Typography variant="body1" paragraph>
              When you connect your Facebook account to our application, we may
              collect the following information:
            </Typography>

            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="h6">Basic Profile Information</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <CheckCircleIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Name and email address"
                      secondary="Used for account identification and communication"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <CheckCircleIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Profile picture"
                      secondary="Displayed in your account settings (optional)"
                    />
                  </ListItem>
                </List>
              </AccordionDetails>
            </Accordion>

            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="h6">Facebook Page Information</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <CheckCircleIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Pages you manage"
                      secondary="List of Facebook pages you have admin access to"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <CheckCircleIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Page details"
                      secondary="Page name, category, profile picture, and basic information"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <CheckCircleIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Page permissions"
                      secondary="Your role and permissions for each page"
                    />
                  </ListItem>
                </List>
              </AccordionDetails>
            </Accordion>

            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="h6">Content and Posts</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <CheckCircleIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Posts created through our platform"
                      secondary="Content, images, and scheduling information"
                    />
                  </ListItem>
                  <ListItem>
                    <ListItemIcon>
                      <CheckCircleIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Post performance data"
                      secondary="Engagement metrics and analytics (when available)"
                    />
                  </ListItem>
                </List>
              </AccordionDetails>
            </Accordion>

            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="h6">Access Tokens</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <List>
                  <ListItem>
                    <ListItemIcon>
                      <CheckCircleIcon color="primary" />
                    </ListItemIcon>
                    <ListItemText
                      primary="Facebook API tokens"
                      secondary="Securely stored tokens for accessing Facebook services on your behalf"
                    />
                  </ListItem>
                </List>
              </AccordionDetails>
            </Accordion>
          </CardContent>
        </Card>

        {/* How We Use Data */}
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Typography
              variant="h5"
              component="h2"
              gutterBottom
              sx={{ display: "flex", alignItems: "center" }}
            >
              <InfoIcon sx={{ mr: 1 }} />
              How We Use Your Information
            </Typography>
            <Typography variant="body1" paragraph>
              We use your Facebook data for the following purposes:
            </Typography>
            <List>
              <ListItem>
                <ListItemIcon>
                  <Typography variant="body1" sx={{ fontWeight: "bold" }}>
                    1.
                  </Typography>
                </ListItemIcon>
                <ListItemText
                  primary="Account Management"
                  secondary="To create and manage your account, authenticate your identity, and provide customer support"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <Typography variant="body1" sx={{ fontWeight: "bold" }}>
                    2.
                  </Typography>
                </ListItemIcon>
                <ListItemText
                  primary="Content Publishing"
                  secondary="To publish posts, images, and other content to your Facebook pages as requested"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <Typography variant="body1" sx={{ fontWeight: "bold" }}>
                    3.
                  </Typography>
                </ListItemIcon>
                <ListItemText
                  primary="Analytics and Insights"
                  secondary="To provide you with performance metrics and insights about your Facebook content"
                />
              </ListItem>
              <ListItem>
                <ListItemIcon>
                  <Typography variant="body1" sx={{ fontWeight: "bold" }}>
                    4.
                  </Typography>
                </ListItemIcon>
                <ListItemText
                  primary="Service Improvement"
                  secondary="To improve our services, develop new features, and enhance user experience"
                />
              </ListItem>
            </List>
          </CardContent>
        </Card>

        {/* Data Sharing */}
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Typography
              variant="h5"
              component="h2"
              gutterBottom
              sx={{ display: "flex", alignItems: "center" }}
            >
              <ShareIcon sx={{ mr: 1 }} />
              Data Sharing and Disclosure
            </Typography>
            <Typography variant="body1" paragraph>
              We do not sell, trade, or rent your Facebook data to third
              parties. We may share your information only in the following
              circumstances:
            </Typography>
            <List>
              <ListItem>
                <ListItemText
                  primary="With Your Consent"
                  secondary="When you explicitly authorize us to share specific information"
                />
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Service Providers"
                  secondary="With trusted third-party service providers who assist in operating our platform (under strict confidentiality agreements)"
                />
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Legal Requirements"
                  secondary="When required by law, court order, or to protect our rights and safety"
                />
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Business Transfers"
                  secondary="In the event of a merger, acquisition, or sale of assets (with prior notice)"
                />
              </ListItem>
            </List>
          </CardContent>
        </Card>

        {/* Data Security */}
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Typography
              variant="h5"
              component="h2"
              gutterBottom
              sx={{ display: "flex", alignItems: "center" }}
            >
              <SecurityIcon sx={{ mr: 1 }} />
              Data Security and Protection
            </Typography>
            <Typography variant="body1" paragraph>
              We implement industry-standard security measures to protect your
              Facebook data:
            </Typography>
            <List>
              <ListItem>
                <ListItemText
                  primary="Encryption"
                  secondary="All data is encrypted in transit and at rest using advanced encryption standards"
                />
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Access Controls"
                  secondary="Strict access controls ensure only authorized personnel can access your data"
                />
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Regular Audits"
                  secondary="We conduct regular security audits and vulnerability assessments"
                />
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Secure Infrastructure"
                  secondary="Our servers are hosted in secure, certified data centers with 24/7 monitoring"
                />
              </ListItem>
            </List>
          </CardContent>
        </Card>

        {/* Your Rights */}
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Typography variant="h5" component="h2" gutterBottom>
              Your Rights and Choices
            </Typography>
            <Typography variant="body1" paragraph>
              You have the following rights regarding your Facebook data:
            </Typography>
            <List>
              <ListItem>
                <ListItemText
                  primary="Access and Portability"
                  secondary="Request a copy of your data in a portable format"
                />
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Correction"
                  secondary="Request correction of inaccurate or incomplete data"
                />
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Deletion"
                  secondary="Request deletion of your data (see our data deletion instructions)"
                />
              </ListItem>
              <ListItem>
                <ListItemText
                  primary="Revoke Access"
                  secondary="Disconnect your Facebook account at any time through your account settings"
                />
              </ListItem>
            </List>
          </CardContent>
        </Card>

        {/* Contact Information */}
        <Card sx={{ mb: 4 }}>
          <CardContent>
            <Typography
              variant="h5"
              component="h2"
              gutterBottom
              sx={{ display: "flex", alignItems: "center" }}
            >
              <ContactMailIcon sx={{ mr: 1 }} />
              Contact Us
            </Typography>
            <Typography variant="body1" paragraph>
              For privacy-related questions or concerns about our Facebook
              integration:
            </Typography>
            <Box sx={{ pl: 2 }}>
              <Typography variant="body1" paragraph>
                <strong>Email:</strong>{" "}
                <Link href="mailto:<EMAIL>" color="primary">
                  <EMAIL>
                </Link>
              </Typography>
              <Typography variant="body1" paragraph>
                <strong>Subject Line:</strong> Facebook Privacy Policy Inquiry
              </Typography>
              <Typography variant="body1" paragraph>
                <strong>Response Time:</strong> We will respond within 5
                business days
              </Typography>
            </Box>
          </CardContent>
        </Card>

        {/* Important Notes */}
        <Alert severity="warning" sx={{ mb: 4 }}>
          <Typography variant="body2">
            <strong>Important Notes:</strong>
          </Typography>
          <List dense>
            <ListItem>
              <ListItemIcon>
                <WarningIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="This policy applies specifically to data collected through Facebook integration" />
            </ListItem>
            <ListItem>
              <ListItemIcon>
                <WarningIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="Facebook's own privacy policy also applies to your Facebook account and data" />
            </ListItem>
            <ListItem>
              <ListItemIcon>
                <WarningIcon fontSize="small" />
              </ListItemIcon>
              <ListItemText primary="We may update this policy periodically - check back for the latest version" />
            </ListItem>
          </List>
        </Alert>

        {/* Footer */}
        <Divider sx={{ my: 3 }} />
        <Box sx={{ textAlign: "center" }}>
          <Typography variant="body2" color="text.secondary">
            This privacy policy complies with Facebook Platform Policy and
            applicable privacy laws.
            <br />
            Last updated: {new Date().toLocaleDateString()}
          </Typography>
        </Box>
      </Paper>
    </Container>
  );
};

export default FacebookPrivacyPolicy;
